<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Grade Details','description' => 'View grade information','backRoute' => route('admin.grading.grades.index'),'backLabel' => 'Back to Grades']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Grade Details','description' => 'View grade information','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.grading.grades.index')),'back-label' => 'Back to Grades']); ?>
        <div class="flex space-x-2">
            <a href="<?php echo e(route('admin.grading.grades.edit', $grade)); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Grade
            </a>
            <form method="POST" action="<?php echo e(route('admin.grading.grades.toggle-published', $grade)); ?>" class="inline">
                <?php echo csrf_field(); ?>
                <button type="submit" class="<?php echo e($grade->is_published ? 'btn-secondary' : 'btn-primary'); ?>">
                    <?php if($grade->is_published): ?>
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                        </svg>
                        Unpublish
                    <?php else: ?>
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        Publish
                    <?php endif; ?>
                </button>
            </form>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Grade Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Student & Subject Info -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Student & Subject Information</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Student</h4>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-600">
                                            <?php echo e(substr($grade->student->first_name, 0, 1)); ?><?php echo e(substr($grade->student->last_name, 0, 1)); ?>

                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900"><?php echo e($grade->student->full_name); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo e($grade->student->student_id); ?></div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Subject</h4>
                            <div class="text-sm font-medium text-gray-900"><?php echo e($grade->subject->name); ?></div>
                            <div class="text-sm text-gray-500"><?php echo e($grade->subject->code); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Grade Details -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Grade Details</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Assessment Type</h4>
                            <div class="flex items-center space-x-2">
                                <span class="badge <?php echo e($grade->gradeCategory->badge_color); ?>">
                                    <?php echo e($grade->gradeCategory->name); ?>

                                </span>
                                <span class="text-sm text-gray-500"><?php echo e(ucfirst($grade->grade_type)); ?></span>
                            </div>
                            <?php if($grade->exam): ?>
                                <div class="text-sm text-gray-500 mt-1">Exam: <?php echo e($grade->exam->title); ?></div>
                            <?php elseif($grade->assignment): ?>
                                <div class="text-sm text-gray-500 mt-1">Assignment: <?php echo e($grade->assignment->title); ?></div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Academic Period</h4>
                            <div class="text-sm text-gray-900"><?php echo e($grade->academicYear->name); ?></div>
                            <div class="text-sm text-gray-500"><?php echo e($grade->academicTerm->name); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Remarks -->
            <?php if($grade->remarks): ?>
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Remarks</h3>
                </div>
                <div class="card-body">
                    <p class="text-sm text-gray-700"><?php echo e($grade->remarks); ?></p>
                </div>
            </div>
            <?php endif; ?>

            <!-- Recorded By -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Recording Information</h3>
                </div>
                <div class="card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Recorded By</h4>
                            <div class="text-sm text-gray-900"><?php echo e($grade->recordedBy->name ?? 'System'); ?></div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Recorded On</h4>
                            <div class="text-sm text-gray-900"><?php echo e($grade->created_at->format('M d, Y \a\t g:i A')); ?></div>
                            <?php if($grade->updated_at != $grade->created_at): ?>
                                <div class="text-sm text-gray-500">Updated: <?php echo e($grade->updated_at->format('M d, Y \a\t g:i A')); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Grade Summary Sidebar -->
        <div class="space-y-6">
            <!-- Grade Score Card -->
            <div class="card">
                <div class="card-body text-center">
                    <div class="text-3xl font-bold text-gray-900 mb-2">
                        <?php echo e($grade->grade_letter); ?>

                    </div>
                    <div class="text-lg text-gray-600 mb-4">
                        <?php echo e(number_format($grade->grade_point, 1)); ?> GPA
                    </div>
                    <div class="text-sm text-gray-500 mb-2">
                        <?php echo e(number_format($grade->marks_obtained, 1)); ?> / <?php echo e(number_format($grade->total_marks, 1)); ?>

                    </div>
                    <div class="text-2xl font-semibold text-gray-900">
                        <?php echo e(number_format($grade->percentage, 1)); ?>%
                    </div>
                </div>
            </div>

            <!-- Status Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Status</h3>
                </div>
                <div class="card-body space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Pass Status</span>
                        <?php if($grade->is_passed): ?>
                            <span class="badge badge-green">Passed</span>
                        <?php else: ?>
                            <span class="badge badge-red">Failed</span>
                        <?php endif; ?>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Publication</span>
                        <?php if($grade->is_published): ?>
                            <span class="badge badge-blue">Published</span>
                        <?php else: ?>
                            <span class="badge badge-gray">Draft</span>
                        <?php endif; ?>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-500">Category Weight</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($grade->gradeCategory->weight); ?>%</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                </div>
                <div class="card-body space-y-3">
                    <a href="<?php echo e(route('admin.grading.grades.edit', $grade)); ?>" 
                       class="w-full btn-secondary text-center">
                        Edit Grade
                    </a>
                    <form method="POST" action="<?php echo e(route('admin.grading.grades.destroy', $grade)); ?>" 
                          onsubmit="return confirm('Are you sure you want to delete this grade?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full btn-secondary text-red-600 hover:text-red-700">
                            Delete Grade
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/grades/show.blade.php ENDPATH**/ ?>