<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Edit Grade Scale','description' => 'Update grading scale information','backRoute' => route('admin.grading.grade-scales.show', $gradeScale),'backLabel' => 'Back to Grade Scale']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Edit Grade Scale','description' => 'Update grading scale information','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.grading.grade-scales.show', $gradeScale)),'back-label' => 'Back to Grade Scale']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Edit Form -->
    <div class="card">
        <form method="POST" action="<?php echo e(route('admin.grading.grade-scales.update', $gradeScale)); ?>" x-data="gradeScaleForm()">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div class="card-body space-y-4">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="name" class="form-label">Scale Name</label>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="<?php echo e(old('name', $gradeScale->name)); ?>" 
                               class="form-input <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               required>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="form-error"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="type" class="form-label">Scale Type</label>
                        <select id="type" 
                                name="type" 
                                x-model="scaleType"
                                @change="updatePresetValues()"
                                class="form-select <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                required>
                            <option value="">Select Type</option>
                            <option value="letter" <?php echo e(old('type', $gradeScale->type) === 'letter' ? 'selected' : ''); ?>>Letter Grade (A, B, C, D, F)</option>
                            <option value="percentage" <?php echo e(old('type', $gradeScale->type) === 'percentage' ? 'selected' : ''); ?>>Percentage Scale</option>
                            <option value="points" <?php echo e(old('type', $gradeScale->type) === 'points' ? 'selected' : ''); ?>>Points Scale (GPA)</option>
                        </select>
                        <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="form-error"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>

                <!-- Status Options -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="is_active" 
                               name="is_active" 
                               value="1"
                               <?php echo e(old('is_active', $gradeScale->is_active) ? 'checked' : ''); ?>

                               class="form-checkbox">
                        <label for="is_active" class="ml-2 text-sm text-gray-700">Active Scale</label>
                    </div>

                    <?php if(!$gradeScale->is_default): ?>
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="is_default" 
                                   name="is_default" 
                                   value="1"
                                   <?php echo e(old('is_default', $gradeScale->is_default) ? 'checked' : ''); ?>

                                   class="form-checkbox">
                            <label for="is_default" class="ml-2 text-sm text-gray-700">Set as Default Scale</label>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Scale Values -->
                <div>
                    <div class="flex items-center justify-between mb-3">
                        <label class="form-label">Scale Values</label>
                        <button type="button"
                                @click="addScaleValue()"
                                class="btn-secondary" style="cursor: pointer;">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Value
                        </button>
                    </div>

                    <!-- Scale Values Header -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-2 mb-2">
                        <div class="grid grid-cols-12 gap-2 text-xs font-medium text-gray-700">
                            <div class="col-span-2">Grade</div>
                            <div class="col-span-2">Min %</div>
                            <div class="col-span-2">Max %</div>
                            <div class="col-span-2" x-show="scaleType === 'points'">Points</div>
                            <div class="col-span-2" x-show="scaleType !== 'points'"></div>
                            <div class="col-span-3">Range Preview</div>
                            <div class="col-span-1">Action</div>
                        </div>
                    </div>

                    <!-- Scale Values List -->
                    <div class="space-y-1">
                        <template x-for="(value, index) in scaleValues" :key="index">
                            <div class="bg-white border border-gray-200 rounded-lg p-2 hover:shadow-sm transition-shadow">
                                <div class="grid grid-cols-12 gap-2 items-center">
                                    <div class="col-span-2">
                                        <input type="text"
                                               :name="`scale_values[${index}][grade]`"
                                               x-model="value.grade"
                                               class="form-input text-center font-medium text-xs h-8"
                                               placeholder="A, B, C..."
                                               required>
                                    </div>

                                    <div class="col-span-2">
                                        <input type="number"
                                               :name="`scale_values[${index}][min]`"
                                               x-model="value.min"
                                               class="form-input text-center text-xs h-8"
                                               min="0"
                                               max="100"
                                               step="0.01"
                                               required>
                                    </div>

                                    <div class="col-span-2">
                                        <input type="number"
                                               :name="`scale_values[${index}][max]`"
                                               x-model="value.max"
                                               class="form-input text-center text-xs h-8"
                                               min="0"
                                               max="100"
                                               step="0.01"
                                               required>
                                    </div>

                                    <div class="col-span-2" x-show="scaleType === 'points'">
                                        <input type="number"
                                               :name="`scale_values[${index}][points]`"
                                               x-model="value.points"
                                               class="form-input text-center text-xs h-8"
                                               min="0"
                                               max="5"
                                               step="0.1">
                                    </div>

                                    <div class="col-span-2" x-show="scaleType !== 'points'"></div>

                                    <div class="col-span-3">
                                        <div class="text-center">
                                            <span class="badge badge-blue text-xs py-1"
                                                  x-text="`${value.min}% - ${value.max}%`"></span>
                                        </div>
                                    </div>

                                    <div class="col-span-1 text-center">
                                        <button type="button"
                                                @click="removeScaleValue(index)"
                                                x-show="scaleValues.length > 1"
                                                class="inline-flex items-center justify-center w-6 h-6 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-full transition-colors"
                                                style="cursor: pointer;"
                                                title="Remove this grade value">
                                            <svg class="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>

                    <?php $__errorArgs = ['scale_values'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="form-error"><?php echo e($message); ?></p>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="<?php echo e(route('admin.grading.grade-scales.show', $gradeScale)); ?>" class="btn-cancel" style="cursor: pointer;">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary" style="cursor: pointer;">
                        Update Grade Scale
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function gradeScaleForm() {
    return {
        scaleType: '<?php echo e(old('type', $gradeScale->type)); ?>',
        scaleValues: <?php echo json_encode(old('scale_values', $gradeScale->scale_values), 512) ?>,

        addScaleValue() {
            this.scaleValues.push({
                min: '',
                max: '',
                grade: '',
                points: ''
            });
        },

        removeScaleValue(index) {
            if (this.scaleValues.length > 1) {
                this.scaleValues.splice(index, 1);
            }
        },

        updatePresetValues() {
            // Only update if current values are empty or user confirms
            if (this.scaleValues.length === 0 || confirm('This will replace current scale values. Continue?')) {
                switch (this.scaleType) {
                    case 'letter':
                        this.scaleValues = [
                            { min: 90, max: 100, grade: 'A', points: 4.0 },
                            { min: 80, max: 89, grade: 'B', points: 3.0 },
                            { min: 70, max: 79, grade: 'C', points: 2.0 },
                            { min: 60, max: 69, grade: 'D', points: 1.0 },
                            { min: 0, max: 59, grade: 'F', points: 0.0 }
                        ];
                        break;
                    case 'percentage':
                        this.scaleValues = [
                            { min: 90, max: 100, grade: 'Excellent', points: 4.0 },
                            { min: 80, max: 89, grade: 'Very Good', points: 3.0 },
                            { min: 70, max: 79, grade: 'Good', points: 2.0 },
                            { min: 60, max: 69, grade: 'Satisfactory', points: 1.0 },
                            { min: 0, max: 59, grade: 'Needs Improvement', points: 0.0 }
                        ];
                        break;
                }
            }
        }
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/grade-scales/edit.blade.php ENDPATH**/ ?>