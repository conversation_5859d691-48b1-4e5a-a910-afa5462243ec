<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Teacher Management','description' => 'Manage teaching staff and their information','backRoute' => route('admin.dashboard'),'backLabel' => 'Back to Dashboard']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Teacher Management','description' => 'Manage teaching staff and their information','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.dashboard')),'back-label' => 'Back to Dashboard']); ?>
        <a href="<?php echo e(route('admin.teachers.create')); ?>" class="btn-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Teacher
        </a>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg p-6"
         x-data="{
             searchQuery: '<?php echo e(request('search')); ?>',
             specialization: '<?php echo e(request('specialization')); ?>',
             qualification: '<?php echo e(request('qualification')); ?>',
             status: '<?php echo e(request('status')); ?>',
             dateFrom: '<?php echo e(request('date_from')); ?>',
             dateTo: '<?php echo e(request('date_to')); ?>',
             showAdvanced: false
         }">

        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
            <!-- Search Input -->
            <div class="flex-1">
                <input
                    type="text"
                    x-model="searchQuery"
                    @input="filterTeachers()"
                    placeholder="Search teachers by name, email, phone, address, employee ID, specialization, qualification, department, experience, or salary..."
                    class="form-input w-full"
                >
            </div>

            <!-- Filter Toggle and Clear -->
            <div class="flex items-center space-x-3">
                <button
                    @click="showAdvanced = !showAdvanced"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                    Filters
                    <svg class="ml-2 h-4 w-4 transition-transform duration-200" :class="{ 'rotate-180': showAdvanced }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <button
                    @click="clearTeacherFilters()"
                    class="inline-flex items-center px-4 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 h-12"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Clear All Filters
                </button>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showAdvanced" x-transition class="mt-6 pt-6 border-t border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- Specialization Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Specialization</label>
                    <select x-model="specialization" @change="filterTeachers()" class="form-select">
                        <option value="">All Specializations</option>
                        <?php $__currentLoopData = $specializations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $spec): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($spec); ?>"><?php echo e($spec); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Qualification Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Qualification</label>
                    <select x-model="qualification" @change="filterTeachers()" class="form-select">
                        <option value="">All Qualifications</option>
                        <?php $__currentLoopData = $qualifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $qual): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($qual); ?>"><?php echo e($qual); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select x-model="status" @change="filterTeachers()" class="form-select">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>

                <!-- Date From -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Hired From</label>
                    <input type="date" x-model="dateFrom" @change="filterTeachers()" class="form-input">
                </div>

                <!-- Date To -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Hired To</label>
                    <input type="date" x-model="dateTo" @change="filterTeachers()" class="form-input">
                </div>
            </div>
        </div>

        <!-- Results Count -->
        <div class="mt-4 flex items-center justify-between">
            <span class="text-sm text-gray-500">
                <span data-results-count><?php echo e($stats['total']); ?></span> teachers found
            </span>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Teachers</dt>
                            <dd class="stat-card-value"><?php echo e($stats['total']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Active</dt>
                            <dd class="stat-card-value"><?php echo e($stats['active']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Inactive</dt>
                            <dd class="stat-card-value"><?php echo e($stats['inactive']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">New This Month</dt>
                            <dd class="stat-card-value"><?php echo e($stats['new_this_month']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Teachers -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">Current Teachers</h3>
                <span class="text-sm text-gray-500">
                    <span data-results-count><?php echo e($stats['total']); ?></span> teachers found
                </span>
            </div>
        </div>
        <div class="p-6">
            <?php if($teachers->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $teachers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teacher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200 card-hover relative"
                             data-filterable
                             data-search-text="<?php echo e($teacher->user->name); ?> <?php echo e($teacher->user->email); ?> <?php echo e($teacher->user->phone ?? ''); ?> <?php echo e($teacher->employee_id); ?> <?php echo e($teacher->specialization); ?> <?php echo e($teacher->qualification); ?> <?php echo e($teacher->hire_date ? $teacher->hire_date->format('M d, Y') : ''); ?> <?php echo e($teacher->hire_date ? $teacher->hire_date->diffForHumans() : ''); ?> <?php echo e($teacher->user->is_active ? 'active' : 'inactive'); ?>"
                             data-specialization="<?php echo e($teacher->specialization); ?>"
                             data-qualification="<?php echo e($teacher->qualification); ?>"
                             data-date="<?php echo e($teacher->hire_date ? $teacher->hire_date->format('Y-m-d') : ''); ?>"
                             data-status="<?php echo e($teacher->user->is_active ? 'active' : 'inactive'); ?>">

                            <!-- 3-dot menu -->
                            <div class="absolute top-2 right-2 z-30" style="right: 8px; top: 8px;">
                                <button class="three-dot-menu p-1 rounded-full hover:bg-gray-100 transition-colors duration-200" onclick="toggleCardMenu(this)">
                                    <svg class="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                                    </svg>
                                </button>
                                <!-- Dropdown menu (hidden by default) -->
                                <div class="card-menu absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-40 hidden">
                                    <div class="py-1">
                                        <a href="<?php echo e(route('admin.teachers.show', $teacher)); ?>" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            View
                                        </a>
                                        <a href="<?php echo e(route('admin.teachers.edit', $teacher)); ?>" class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                            Edit
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-14 h-14 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-md">
                                        <span class="text-white font-bold text-lg">
                                            <?php echo e(substr($teacher->user->name, 0, 2)); ?>

                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4 flex-1">
                                    <h4 class="text-lg font-semibold text-gray-900"><?php echo e($teacher->user->name); ?></h4>
                                    <p class="text-sm text-gray-600 mt-1">ID: <?php echo e($teacher->employee_id); ?></p>
                                    <div class="flex items-center mt-2 space-x-2">
                                        <span class="badge badge-purple">
                                            <?php echo e($teacher->specialization); ?>

                                        </span>
                                        <?php if($teacher->qualification): ?>
                                            <span class="badge badge-blue">
                                                <?php echo e($teacher->qualification); ?>

                                            </span>
                                        <?php endif; ?>
                                        <span class="badge <?php echo e($teacher->user->is_active ? 'badge-green' : 'badge-red'); ?>">
                                            <?php echo e($teacher->user->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 pt-4 border-t border-gray-100">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">Contact</p>
                                        <p class="text-sm text-gray-600 mt-1"><?php echo e($teacher->user->email); ?></p>
                                        <p class="text-sm text-gray-600"><?php echo e($teacher->user->phone ?? 'No phone number'); ?></p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-700">Employment</p>
                                        <?php if($teacher->hire_date): ?>
                                            <p class="text-sm text-gray-600 mt-1">
                                                Hired: <?php echo e($teacher->hire_date->format('M d, Y')); ?>

                                            </p>
                                            <p class="text-sm text-gray-600 mt-1">
                                                <?php echo e($teacher->hire_date->diffForHumans()); ?>

                                            </p>
                                        <?php else: ?>
                                            <p class="text-sm text-gray-600 mt-1">Hire date not set</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>


                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <p class="text-center text-gray-500 py-8">No teachers found</p>
            <?php endif; ?>
        </div>
    </div>

</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle card menu function
function toggleCardMenu(button) {
    const menu = button.nextElementSibling;
    const allMenus = document.querySelectorAll('.card-menu');

    // Close all other menus
    allMenus.forEach(m => {
        if (m !== menu) {
            m.classList.add('hidden');
        }
    });

    // Toggle current menu
    menu.classList.toggle('hidden');
}

// Close menus when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('.three-dot-menu') && !event.target.closest('.card-menu')) {
        const allMenus = document.querySelectorAll('.card-menu');
        allMenus.forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});

// Teacher filtering functionality
function filterTeachers() {
    const searchQuery = document.querySelector('[x-model="searchQuery"]').value.toLowerCase();
    const specializationFilter = document.querySelector('[x-model="specialization"]').value;
    const qualificationFilter = document.querySelector('[x-model="qualification"]').value;
    const statusFilter = document.querySelector('[x-model="status"]').value;
    const dateFromFilter = document.querySelector('[x-model="dateFrom"]').value;
    const dateToFilter = document.querySelector('[x-model="dateTo"]').value;

    const teachers = document.querySelectorAll('[data-filterable]');
    let visibleCount = 0;

    teachers.forEach(teacher => {
        const searchText = teacher.getAttribute('data-search-text').toLowerCase();
        const teacherSpecialization = teacher.getAttribute('data-specialization');
        const teacherQualification = teacher.getAttribute('data-qualification');
        const teacherStatus = teacher.getAttribute('data-status');
        const teacherDate = teacher.getAttribute('data-date');

        let isVisible = true;

        // Search filter
        if (searchQuery && !searchText.includes(searchQuery)) {
            isVisible = false;
        }

        // Specialization filter
        if (specializationFilter && (!teacherSpecialization || !teacherSpecialization.toLowerCase().includes(specializationFilter.toLowerCase()))) {
            isVisible = false;
        }

        // Qualification filter
        if (qualificationFilter && (!teacherQualification || !teacherQualification.toLowerCase().includes(qualificationFilter.toLowerCase()))) {
            isVisible = false;
        }

        // Status filter
        if (statusFilter && teacherStatus !== statusFilter) {
            isVisible = false;
        }

        // Date range filter
        if (dateFromFilter && teacherDate && teacherDate < dateFromFilter) {
            isVisible = false;
        }

        if (dateToFilter && teacherDate && teacherDate > dateToFilter) {
            isVisible = false;
        }

        // Show/hide teacher
        teacher.style.display = isVisible ? '' : 'none';
        if (isVisible) visibleCount++;
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = visibleCount;
    }
}

// Clear all filters
function clearTeacherFilters() {
    // Reset form inputs and trigger events
    const searchInput = document.querySelector('[x-model="searchQuery"]');
    const specializationInput = document.querySelector('[x-model="specialization"]');
    const qualificationInput = document.querySelector('[x-model="qualification"]');
    const statusInput = document.querySelector('[x-model="status"]');
    const dateFromInput = document.querySelector('[x-model="dateFrom"]');
    const dateToInput = document.querySelector('[x-model="dateTo"]');

    if (searchInput) {
        searchInput.value = '';
        searchInput.dispatchEvent(new Event('input'));
    }
    if (specializationInput) {
        specializationInput.value = '';
        specializationInput.dispatchEvent(new Event('change'));
    }
    if (qualificationInput) {
        qualificationInput.value = '';
        qualificationInput.dispatchEvent(new Event('change'));
    }
    if (statusInput) {
        statusInput.value = '';
        statusInput.dispatchEvent(new Event('change'));
    }
    if (dateFromInput) {
        dateFromInput.value = '';
        dateFromInput.dispatchEvent(new Event('change'));
    }
    if (dateToInput) {
        dateToInput.value = '';
        dateToInput.dispatchEvent(new Event('change'));
    }

    // Show all teachers
    const teachers = document.querySelectorAll('[data-filterable]');
    teachers.forEach(teacher => {
        teacher.style.display = '';
    });

    // Update results count
    const resultsCount = document.querySelector('[data-results-count]');
    if (resultsCount) {
        resultsCount.textContent = teachers.length;
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/teachers/index.blade.php ENDPATH**/ ?>