@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <x-page-header
        title="Grade Reports"
        description="Generate and view comprehensive grade reports"
        :back-route="route('admin.dashboard')"
        back-label="Back to Dashboard">
    </x-page-header>

    <!-- Statistics Cards -->
    <div class="grid-stats-4">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-blue-100">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ $stats['total_students'] }}</p>
                        <p class="stat-card-label">Total Students</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-green-100">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ number_format($stats['total_grades']) }}</p>
                        <p class="stat-card-label">Total Grades</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-yellow-100">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ number_format($stats['average_performance'], 1) }}%</p>
                        <p class="stat-card-label">Average Performance</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="stat-card-icon bg-purple-100">
                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="stat-card-value">{{ number_format($stats['pass_rate'], 1) }}%</p>
                        <p class="stat-card-label">Pass Rate</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Types -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Student Report -->
        <div class="card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Student Report</h3>
                        <p class="text-sm text-gray-500">Individual student performance</p>
                    </div>
                </div>
                <div class="mt-6">
                    <button onclick="openReportModal('student')" class="btn-primary w-full">
                        Generate Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Class Report -->
        <div class="card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Class Report</h3>
                        <p class="text-sm text-gray-500">Class-wide performance analysis</p>
                    </div>
                </div>
                <div class="mt-6">
                    <button onclick="openReportModal('class')" class="btn-primary w-full">
                        Generate Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Subject Report -->
        <div class="card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Subject Report</h3>
                        <p class="text-sm text-gray-500">Subject performance analysis</p>
                    </div>
                </div>
                <div class="mt-6">
                    <button onclick="openReportModal('subject')" class="btn-primary w-full">
                        Generate Report
                    </button>
                </div>
            </div>
        </div>

        <!-- Term Report -->
        <div class="card">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">Term Report</h3>
                        <p class="text-sm text-gray-500">Term-wise performance summary</p>
                    </div>
                </div>
                <div class="mt-6">
                    <button onclick="openReportModal('term')" class="btn-primary w-full">
                        Generate Report
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Modals -->
@include('admin.grade-reports.modals.student-report')
@include('admin.grade-reports.modals.class-report')
@include('admin.grade-reports.modals.subject-report')
@include('admin.grade-reports.modals.term-report')

@push('scripts')
<script>
function openReportModal(type) {
    const modal = document.getElementById(type + 'ReportModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

function closeReportModal(type) {
    const modal = document.getElementById(type + 'ReportModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modals = ['studentReportModal', 'classReportModal', 'subjectReportModal', 'termReportModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal && !modal.classList.contains('hidden')) {
            const modalContent = modal.querySelector('.modal-content');
            if (!modalContent.contains(event.target)) {
                modal.classList.add('hidden');
            }
        }
    });
});
</script>
@endpush
@endsection
